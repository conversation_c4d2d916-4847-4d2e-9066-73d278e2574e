/**
 * Swagger Configuration for Bot Builder Service
 *
 * OpenAPI 3.0 specification for all administrative APIs
 * Optimized implementation without Zod dependencies
 */

import swaggerJsdoc from "swagger-jsdoc";
import { SwaggerDefinition } from "swagger-jsdoc";

// Direct OpenAPI schema definitions - optimized without Zod dependencies

// Direct OpenAPI component schemas - optimized without Zod dependencies
const components = {
  schemas: {
    // Common Response Schemas
    ApiResponse: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          description: "Indicates if the request was successful",
        },
        data: {
          type: "object",
          description: "Response data",
        },
        timestamp: {
          type: "string",
          format: "date-time",
          description: "Response timestamp",
        },
        requestId: {
          type: "string",
          description: "Unique request identifier",
        },
      },
      required: ["success", "timestamp"],
    },

    // Error Response Schemas
    ValidationErrorResponse: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          example: false,
        },
        error: {
          type: "object",
          properties: {
            code: {
              type: "string",
              example: "VALIDATION_ERROR",
            },
            message: {
              type: "string",
              example: "Validation failed",
            },
            details: {
              type: "object",
              additionalProperties: true,
            },
          },
        },
        timestamp: {
          type: "string",
          format: "date-time",
        },
      },
    },

    NotFoundErrorResponse: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          example: false,
        },
        error: {
          type: "object",
          properties: {
            code: {
              type: "string",
              example: "NOT_FOUND",
            },
            message: {
              type: "string",
              example: "Resource not found",
            },
          },
        },
        timestamp: {
          type: "string",
          format: "date-time",
        },
      },
    },

    InternalServerErrorResponse: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          example: false,
        },
        error: {
          type: "object",
          properties: {
            code: {
              type: "string",
              example: "INTERNAL_ERROR",
            },
            message: {
              type: "string",
              example: "Internal server error",
            },
          },
        },
        timestamp: {
          type: "string",
          format: "date-time",
        },
      },
    },

    // Pagination Schema
    Pagination: {
      type: "object",
      properties: {
        page: {
          type: "integer",
          minimum: 1,
          description: "Current page number",
        },
        limit: {
          type: "integer",
          minimum: 1,
          maximum: 100,
          description: "Number of items per page",
        },
        total: {
          type: "integer",
          minimum: 0,
          description: "Total number of items",
        },
        totalPages: {
          type: "integer",
          minimum: 0,
          description: "Total number of pages",
        },
        hasNext: {
          type: "boolean",
          description: "Whether there is a next page",
        },
        hasPrev: {
          type: "boolean",
          description: "Whether there is a previous page",
        },
      },
      required: ["page", "limit", "total", "totalPages", "hasNext", "hasPrev"],
    },

    // App Schemas
    CreateAppRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 255,
          description: "Application name",
          example: "Customer Support Bot",
        },
        desc: {
          type: "string",
          maxLength: 1000,
          description: "Application description",
          example: "A comprehensive customer support chatbot",
        },
        appData: {
          type: "object",
          description: "Application configuration data",
          example: {
            version: "1.0",
            modules: ["chat", "analytics"],
          },
        },
        alignment: {
          type: "string",
          enum: ["left", "center", "right"],
          description: "UI alignment preference",
          example: "center",
        },
        svg: {
          type: "string",
          description: "SVG icon for the application",
          example: "<svg>...</svg>",
        },
      },
      required: ["name"],
    },

    UpdateAppRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 255,
          description: "Application name",
        },
        desc: {
          type: "string",
          maxLength: 1000,
          description: "Application description",
        },
        appData: {
          type: "object",
          description: "Application configuration data",
        },
        alignment: {
          type: "string",
          enum: ["left", "center", "right"],
          description: "UI alignment preference",
        },
        svg: {
          type: "string",
          description: "SVG icon for the application",
        },
      },
    },

    App: {
      type: "object",
      properties: {
        id: {
          type: "string",
          format: "uuid",
          description: "Unique application identifier",
        },
        name: {
          type: "string",
          description: "Application name",
        },
        desc: {
          type: "string",
          description: "Application description",
        },
        status: {
          type: "integer",
          description: "Application status",
        },
        appData: {
          type: "object",
          description: "Application configuration data",
        },
        owner: {
          type: "integer",
          description: "Owner user ID",
        },
        createdBy: {
          type: "integer",
          description: "Creator user ID",
        },
        modifiedBy: {
          type: "integer",
          description: "Last modifier user ID",
        },
        createdAt: {
          type: "string",
          format: "date-time",
          description: "Creation timestamp",
        },
        updatedAt: {
          type: "string",
          format: "date-time",
          description: "Last update timestamp",
        },
      },
    },

    PaginatedApps: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: {
            $ref: "#/components/schemas/App",
          },
        },
        pagination: {
          $ref: "#/components/schemas/Pagination",
        },
      },
    },

    // Bot Schemas
    CreateBotRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 255,
          description: "Bot name",
          example: "Customer Support Bot",
        },
        description: {
          type: "string",
          maxLength: 1000,
          description: "Bot description",
          example: "Handles customer inquiries and support requests",
        },
        settings: {
          type: "object",
          description: "Bot configuration settings",
          example: {
            nlu: {
              provider: "rasa",
              confidenceThreshold: 0.7,
            },
            fallback: {
              enabled: true,
              message: "I didn't understand that. Can you please rephrase?",
            },
          },
        },
        metadata: {
          type: "object",
          description: "Additional bot metadata",
          example: {
            category: "support",
            version: "1.0",
          },
        },
      },
      required: ["name"],
    },

    UpdateBotRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 255,
          description: "Bot name",
        },
        description: {
          type: "string",
          maxLength: 1000,
          description: "Bot description",
        },
        settings: {
          type: "object",
          description: "Bot configuration settings",
        },
        metadata: {
          type: "object",
          description: "Additional bot metadata",
        },
      },
    },

    Bot: {
      type: "object",
      properties: {
        id: {
          type: "string",
          format: "uuid",
          description: "Unique bot identifier",
        },
        name: {
          type: "string",
          description: "Bot name",
        },
        description: {
          type: "string",
          description: "Bot description",
        },
        settings: {
          type: "object",
          description: "Bot configuration settings",
        },
        metadata: {
          type: "object",
          description: "Additional bot metadata",
        },
        isActive: {
          type: "boolean",
          description: "Whether the bot is active",
        },
        createdAt: {
          type: "string",
          format: "date-time",
          description: "Creation timestamp",
        },
        updatedAt: {
          type: "string",
          format: "date-time",
          description: "Last update timestamp",
        },
      },
    },

    PaginatedBots: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: {
            $ref: "#/components/schemas/Bot",
          },
        },
        pagination: {
          $ref: "#/components/schemas/Pagination",
        },
      },
    },

    CreateChannelIntegrationRequest: {
      type: "object",
      properties: {
        channelType: {
          type: "string",
          enum: ["whatsapp", "telegram", "facebook", "web", "sms"],
          description: "Type of channel integration",
          example: "whatsapp",
        },
        config: {
          type: "object",
          description: "Channel-specific configuration",
          example: {
            phoneNumber: "+1234567890",
            apiKey: "your-api-key",
            webhookUrl: "https://your-domain.com/webhook",
          },
        },
      },
      required: ["channelType", "config"],
    },

    UpdateChannelIntegrationRequest: {
      type: "object",
      properties: {
        config: {
          type: "object",
          description: "Updated channel-specific configuration",
        },
      },
      required: ["config"],
    },

    // Language Schemas
    Language: {
      type: "object",
      properties: {
        id: {
          type: "string",
          format: "uuid",
          description: "Unique language identifier",
        },
        name: {
          type: "string",
          description: "Language name",
          example: "English",
        },
        code: {
          type: "string",
          description: "ISO language code",
          example: "en",
        },
        isActive: {
          type: "boolean",
          description: "Whether the language is active",
        },
        createdAt: {
          type: "string",
          format: "date-time",
          description: "Creation timestamp",
        },
        updatedAt: {
          type: "string",
          format: "date-time",
          description: "Last update timestamp",
        },
      },
    },

    CreateLanguageRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 100,
          description: "Language name",
          example: "Spanish",
        },
        code: {
          type: "string",
          minLength: 2,
          maxLength: 5,
          description: "ISO language code",
          example: "es",
        },
      },
      required: ["name", "code"],
    },

    UpdateLanguageRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 100,
          description: "Language name",
        },
        isActive: {
          type: "boolean",
          description: "Whether the language is active",
        },
      },
    },

    PaginatedLanguages: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: {
            $ref: "#/components/schemas/Language",
          },
        },
        pagination: {
          $ref: "#/components/schemas/Pagination",
        },
      },
    },

    // Flow Schemas
    Flow: {
      type: "object",
      properties: {
        id: {
          type: "string",
          format: "uuid",
          description: "Unique flow identifier",
        },
        name: {
          type: "string",
          description: "Flow name",
        },
        description: {
          type: "string",
          description: "Flow description",
        },
        botId: {
          type: "string",
          format: "uuid",
          description: "Associated bot ID",
        },
        nodes: {
          type: "array",
          items: {
            type: "object",
          },
          description: "Flow nodes configuration",
        },
        edges: {
          type: "array",
          items: {
            type: "object",
          },
          description: "Flow edges configuration",
        },
        isActive: {
          type: "boolean",
          description: "Whether the flow is active",
        },
        createdAt: {
          type: "string",
          format: "date-time",
          description: "Creation timestamp",
        },
        updatedAt: {
          type: "string",
          format: "date-time",
          description: "Last update timestamp",
        },
      },
    },

    CreateFlowRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 255,
          description: "Flow name",
          example: "Welcome Flow",
        },
        description: {
          type: "string",
          maxLength: 1000,
          description: "Flow description",
          example: "Initial welcome flow for new users",
        },
        nodes: {
          type: "array",
          items: {
            type: "object",
          },
          description: "Flow nodes configuration",
          example: [
            {
              id: "start",
              type: "start",
              position: { x: 100, y: 100 },
            },
          ],
        },
        edges: {
          type: "array",
          items: {
            type: "object",
          },
          description: "Flow edges configuration",
          example: [],
        },
      },
      required: ["name", "nodes", "edges"],
    },

    UpdateFlowRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 255,
          description: "Flow name",
        },
        description: {
          type: "string",
          maxLength: 1000,
          description: "Flow description",
        },
        nodes: {
          type: "array",
          items: {
            type: "object",
          },
          description: "Flow nodes configuration",
        },
        edges: {
          type: "array",
          items: {
            type: "object",
          },
          description: "Flow edges configuration",
        },
        isActive: {
          type: "boolean",
          description: "Whether the flow is active",
        },
      },
    },

    PaginatedFlows: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: {
            $ref: "#/components/schemas/Flow",
          },
        },
        pagination: {
          $ref: "#/components/schemas/Pagination",
        },
      },
    },

    // FAQ Category Schemas
    FaqCategory: {
      type: "object",
      properties: {
        id: {
          type: "string",
          format: "uuid",
          description: "Unique FAQ category identifier",
        },
        name: {
          type: "string",
          description: "Category name",
        },
        description: {
          type: "string",
          description: "Category description",
        },
        botId: {
          type: "string",
          format: "uuid",
          description: "Associated bot ID",
        },
        isActive: {
          type: "boolean",
          description: "Whether category is active",
        },
        createdAt: {
          type: "string",
          format: "date-time",
          description: "Creation timestamp",
        },
        updatedAt: {
          type: "string",
          format: "date-time",
          description: "Last update timestamp",
        },
        createdBy: {
          type: "string",
          format: "uuid",
          description: "Creator user ID",
        },
        updatedBy: {
          type: "string",
          format: "uuid",
          description: "Last modifier user ID",
        },
      },
    },

    CreateFaqCategoryRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 255,
          description: "Category name",
          example: "General Questions",
        },
        description: {
          type: "string",
          maxLength: 1000,
          description: "Category description",
          example: "General questions about our services",
        },
        botId: {
          type: "string",
          format: "uuid",
          description: "Associated bot ID",
          example: "123e4567-e89b-12d3-a456-************",
        },
      },
      required: ["name", "botId"],
    },

    UpdateFaqCategoryRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 255,
          description: "Category name",
        },
        description: {
          type: "string",
          maxLength: 1000,
          description: "Category description",
        },
        isActive: {
          type: "boolean",
          description: "Whether category is active",
        },
      },
    },

    PaginatedFaqCategories: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: {
            $ref: "#/components/schemas/FaqCategory",
          },
        },
        pagination: {
          $ref: "#/components/schemas/Pagination",
        },
      },
    },

    // FAQ Item Schemas
    FaqItem: {
      type: "object",
      properties: {
        id: {
          type: "string",
          format: "uuid",
          description: "Unique FAQ item identifier",
        },
        botId: {
          type: "string",
          format: "uuid",
          description: "Associated bot ID",
        },
        categoryId: {
          type: "string",
          format: "uuid",
          description: "Associated category ID",
        },
        flowId: {
          type: "string",
          format: "uuid",
          nullable: true,
          description: "Optional associated flow ID",
        },
        question: {
          type: "string",
          description: "FAQ question",
        },
        answer: {
          type: "string",
          description: "FAQ answer",
        },
        isActive: {
          type: "boolean",
          description: "Whether FAQ item is active",
        },
        createdAt: {
          type: "string",
          format: "date-time",
          description: "Creation timestamp",
        },
        updatedAt: {
          type: "string",
          format: "date-time",
          description: "Last update timestamp",
        },
        createdBy: {
          type: "string",
          format: "uuid",
          description: "Creator user ID",
        },
        updatedBy: {
          type: "string",
          format: "uuid",
          description: "Last modifier user ID",
        },
      },
    },

    CreateFaqItemRequest: {
      type: "object",
      properties: {
        botId: {
          type: "string",
          format: "uuid",
          description: "Associated bot ID",
          example: "123e4567-e89b-12d3-a456-************",
        },
        categoryId: {
          type: "string",
          format: "uuid",
          description: "Associated category ID",
          example: "123e4567-e89b-12d3-a456-************",
        },
        flowId: {
          type: "string",
          format: "uuid",
          nullable: true,
          description: "Optional associated flow ID",
          example: "123e4567-e89b-12d3-a456-************",
        },
        question: {
          type: "string",
          minLength: 1,
          maxLength: 1000,
          description: "FAQ question",
          example: "What are your business hours?",
        },
        answer: {
          type: "string",
          minLength: 1,
          maxLength: 5000,
          description: "FAQ answer",
          example: "Our business hours are Monday to Friday, 9 AM to 6 PM.",
        },
      },
      required: ["botId", "categoryId", "question", "answer"],
    },

    UpdateFaqItemRequest: {
      type: "object",
      properties: {
        categoryId: {
          type: "string",
          format: "uuid",
          description: "Associated category ID",
        },
        flowId: {
          type: "string",
          format: "uuid",
          nullable: true,
          description: "Optional associated flow ID",
        },
        question: {
          type: "string",
          minLength: 1,
          maxLength: 1000,
          description: "FAQ question",
        },
        answer: {
          type: "string",
          minLength: 1,
          maxLength: 5000,
          description: "FAQ answer",
        },
        isActive: {
          type: "boolean",
          description: "Whether FAQ item is active",
        },
      },
    },

    PaginatedFaqItems: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: {
            $ref: "#/components/schemas/FaqItem",
          },
        },
        pagination: {
          $ref: "#/components/schemas/Pagination",
        },
      },
    },

    // Entity Schemas
    Entity: {
      type: "object",
      properties: {
        id: {
          type: "string",
          format: "uuid",
          description: "Unique entity identifier",
        },
        name: {
          type: "string",
          description: "Entity name",
        },
        botId: {
          type: "string",
          format: "uuid",
          description: "Associated bot ID",
        },
        intentId: {
          type: "string",
          format: "uuid",
          description: "Associated intent ID",
        },
        metadata: {
          type: "object",
          nullable: true,
          description: "Additional entity metadata",
        },
        createdAt: {
          type: "string",
          format: "date-time",
          description: "Creation timestamp",
        },
        updatedAt: {
          type: "string",
          format: "date-time",
          description: "Last update timestamp",
        },
        createdBy: {
          type: "string",
          format: "uuid",
          description: "Creator user ID",
        },
        updatedBy: {
          type: "string",
          format: "uuid",
          description: "Last modifier user ID",
        },
      },
    },

    CreateEntityRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 100,
          description: "Entity name",
          example: "person",
        },
        botId: {
          type: "string",
          format: "uuid",
          description: "Associated bot ID",
          example: "123e4567-e89b-12d3-a456-************",
        },
        intentId: {
          type: "string",
          format: "uuid",
          description: "Associated intent ID",
          example: "123e4567-e89b-12d3-a456-************",
        },
        metadata: {
          type: "object",
          description: "Additional entity metadata",
          example: {
            type: "custom",
            values: ["John", "Jane", "Bob"],
          },
        },
      },
      required: ["name", "botId", "intentId"],
    },

    UpdateEntityRequest: {
      type: "object",
      properties: {
        name: {
          type: "string",
          minLength: 1,
          maxLength: 100,
          description: "Entity name",
        },
        intentId: {
          type: "string",
          format: "uuid",
          description: "Associated intent ID",
        },
        metadata: {
          type: "object",
          description: "Additional entity metadata",
        },
      },
    },

    PaginatedEntities: {
      type: "object",
      properties: {
        items: {
          type: "array",
          items: {
            $ref: "#/components/schemas/Entity",
          },
        },
        pagination: {
          $ref: "#/components/schemas/Pagination",
        },
      },
    },
  },
};

const swaggerDefinition: SwaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "Bot Builder Service API",
    version: "1.0.0",
    description:
      "Administrative API for managing bots, flows, and configurations in the no-code chatbot platform",
    contact: {
      name: "Chatbot Platform Team",
      email: "<EMAIL>",
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT",
    },
  },
  servers: [
    {
      url: "http://localhost:3000",
      description: "Development server",
    },
    {
      url: "https://api.chatbot-platform.com",
      description: "Production server",
    },
  ],
  components: {
    securitySchemes: {
      BearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "JWT token for user authentication",
      },
    },
    ...components,
  },
  tags: [
    {
      name: "Apps",
      description: "Application management operations",
    },
    {
      name: "Bots",
      description: "Bot management operations",
    },
    {
      name: "Flows",
      description: "Flow management operations",
    },
    {
      name: "Health",
      description: "Health check endpoints",
    },
    {
      name: "FAQ Categories",
      description: "FAQ category management operations",
    },
    {
      name: "FAQ Items",
      description: "FAQ item management operations",
    },
    {
      name: "FAQ Translations",
      description: "FAQ translation management operations",
    },
    {
      name: "Intent Items",
      description: "Intent item management operations",
    },
    {
      name: "Intent Utterances",
      description: "Intent utterance management operations",
    },
    {
      name: "Intent Utterance Translations",
      description: "Intent utterance translation management operations",
    },
    {
      name: "Entities",
      description: "Entity management operations",
    },
    {
      name: "Languages",
      description: "Language management operations",
    },
    {
      name: "Bot Languages",
      description: "Bot language configuration operations",
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: ["./src/controllers/*.ts", "./src/routers/*.ts", "./src/app.ts"],
  swaggerOptions: {
    persistAuthorization: true,
  },
};

export const swaggerSpec = swaggerJsdoc(options);
export default swaggerSpec;
