import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { CreateFaqCategoryRequest, UpdateFaqCategoryRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export class FaqCategoryController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/faq-categories:
   *   post:
   *     summary: Create a new FAQ category
   *     description: Creates a new FAQ category for organizing FAQ items within a bot
   *     tags: [FAQ Categories]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateFaqCategoryRequest'
   *           example:
   *             name: "General Questions"
   *             description: "General questions about our services"
   *             botId: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       201:
   *         description: FAQ category created successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/FaqCategory'
   *       400:
   *         description: Validation error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public create = async (
    req: Request<any, any, CreateFaqCategoryRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const faqCategory = await this.models.FaqCategory.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`FAQ category created: ${faqCategory.id}`);
      res.status(201).json(successResponse(faqCategory));
    } catch (error) {
      logger.error("Error creating FAQ category:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-categories:
   *   get:
   *     summary: Get all FAQ categories
   *     description: Retrieves a paginated list of FAQ categories with optional filtering
   *     tags: [FAQ Categories]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number for pagination
   *         example: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: Number of items per page
   *         example: 10
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term to filter categories by name
   *         example: "general"
   *       - in: query
   *         name: botId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by bot ID
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: FAQ categories retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/PaginatedFaqCategories'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.FaqCategory, req.query, ["name"]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching FAQ categories:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ categories",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-categories/{id}:
   *   get:
   *     summary: Get FAQ category by ID
   *     description: Retrieves a specific FAQ category by its unique identifier
   *     tags: [FAQ Categories]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: FAQ category unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: FAQ category retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/FaqCategory'
   *       404:
   *         description: FAQ category not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const faqCategory = await this.models.FaqCategory.findOne({
        where: { id },
      });

      if (!faqCategory) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ category not found" }));
        return;
      }

      res.json(successResponse(faqCategory));
    } catch (error) {
      logger.error(`Error fetching FAQ category ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch FAQ category" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-categories/{id}:
   *   put:
   *     summary: Update an FAQ category
   *     description: Updates an existing FAQ category with the provided data
   *     tags: [FAQ Categories]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: FAQ category unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateFaqCategoryRequest'
   *           example:
   *             name: "Updated General Questions"
   *             description: "Updated description for general questions"
   *             isActive: true
   *     responses:
   *       200:
   *         description: FAQ category updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/FaqCategory'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponse'
   *       404:
   *         description: FAQ category not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public update = async (
    req: Request<UuidParams, any, UpdateFaqCategoryRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.FaqCategory.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ category not found" }));
        return;
      }

      const faqCategory = await this.models.FaqCategory.findByPk(id);
      logger.info(`FAQ category updated: ${id}`);

      res.json(successResponse(faqCategory));
    } catch (error) {
      logger.error(`Error updating FAQ category ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-categories/{id}:
   *   delete:
   *     summary: Delete an FAQ category
   *     description: Permanently deletes an FAQ category and all its associated data
   *     tags: [FAQ Categories]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: FAQ category unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       204:
   *         description: FAQ category deleted successfully (no content)
   *       404:
   *         description: FAQ category not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.FaqCategory.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ category not found" }));
        return;
      }

      logger.info(`FAQ category deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting FAQ category ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to delete FAQ category",
        }),
      );
    }
  };
}
