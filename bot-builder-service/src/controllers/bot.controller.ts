/**
 * Bot Controller
 *
 * Handles HTTP requests for bot management operations.
 *
 * @swagger
 * components:
 *   schemas:
 *     BotResponse:
 *       allOf:
 *         - type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
 *         - type: object
 *           properties:
 *             data:
 *               type: object
                   properties:
                     id:
                       type: string
                       format: uuid
                     name:
                       type: string
                     description:
                       type: string
 *     BotsResponse:
 *       allOf:
 *         - type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
 *         - type: object
 *           properties:
 *             data:
 *               allOf:
 *                 - type: object
                 properties:
                   page:
                     type: integer
                   limit:
                     type: integer
                   total:
                     type: integer
 *                 - type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
                   properties:
                     id:
                       type: string
                       format: uuid
                     name:
                       type: string
                     description:
                       type: string
 *     BuildBotResponse:
 *       allOf:
 *         - type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
 *         - type: object
 *           properties:
 *             data:
 *               type: object
                   properties:
                     success:
                       type: boolean
                     message:
                       type: string
 */

import { Request, Response } from "express";
import { AppContext } from "../types/context.types";
import { BotService } from "../services/bot.service";
import { CreateBotRequest, UpdateBotRequest, BuildBotResponse } from "../types";
import {
  getPaginatedResults,
  logger,
  PaginationQuery,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { Models } from "@neuratalk/bot-store";

export class BotController {
  private botService: BotService;
  private models: Models;

  constructor(context: AppContext) {
    this.botService = context.botService;
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/bots:
   *   post:
   *     summary: Create a new bot
   *     description: Creates a new chatbot with the specified configuration
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
             properties:
               name:
                 type: string
               description:
                 type: string
   *           example:
   *             name: "Customer Support Bot"
   *             description: "Handles customer inquiries and support requests"
   *             settings:
   *               nlu:
   *                 provider: "rasa"
   *                 confidenceThreshold: 0.7
   *     responses:
   *       201:
   *         description: Bot created successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Bot'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public createBot = async (
    req: Request<any, any, CreateBotRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user?.id;

      const bot = await this.botService.createBot(req.body, userId);

      res.status(201).json(successResponse(bot));
    } catch (error) {
      logger.error("Error in createBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to create bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   get:
   *     summary: Get bot by ID
   *     description: Retrieves a specific bot by its unique identifier
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 id:
                   type: string
                   format: uuid
                 name:
                   type: string
                 description:
                   type: string
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
   */
  public getBotById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const bot = await this.botService.getBotById(id);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in getBotById controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to get bot" }));
    }
  };

  /**
   * PUT /api/v1/bots/{id}
   * Update bot
   */
  public updateBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const request: UpdateBotRequest = req.body;
      const userId = req.user?.id;

      const bot = await this.botService.updateBot(id, request, userId);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in updateBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to update bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   delete:
   *     summary: Delete bot
   *     description: Permanently deletes a bot and all its associated data
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       204:
   *         description: Bot deleted successfully
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public deleteBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const deleted = await this.botService.deleteBot(id);

      if (!deleted) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.status(204).send();
    } catch (error) {
      logger.error("Error in deleteBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to delete bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots:
   *   get:
   *     summary: Get all bots
   *     description: Retrieves a paginated list of bots with optional filtering and search capabilities
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: Number of items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term to filter bots by name
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include
   *     responses:
   *       200:
   *         description: List of bots with pagination
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/PaginatedBots'
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  getBots = async (req: Request<any, any, any, PaginationQuery>, res: Response): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.Bot, req.query as PaginationQuery, [
        "name",
      ]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching Bots:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch Bots" }));
    }
  };

  /**
   * POST /api/v1/bots/{id}/activate
   * Activate bot
   */
  activateBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      const bot = await this.botService.activateBot(id, userId);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in activateBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to activate bot" }));
    }
  };

  /**
   * POST /api/v1/bots/{id}/deactivate
   * Deactivate bot
   */
  deactivateBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      const bot = await this.botService.deactivateBot(id, userId);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in deactivateBot controller:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to deactivate bot" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}/build:
   *   post:
   *     summary: Build a Rasa bot
   *     description: Generates Rasa NLU files for the specified bot in the rasa-nlu directory
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot built successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 success:
                   type: boolean
                 message:
                   type: string
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
   */
  buildBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      // Build the bot
      const result = await this.botService.buildBot(id);

      res.json(successResponse<BuildBotResponse>(result));
    } catch (error: any) {
      logger.error("Error in buildBot controller:", error);

      // Handle not found error
      if (error.message && error.message.includes("not found")) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to build bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{botId}/channels/{channelType}:
   *   get:
   *     summary: Get channel configuration
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: channelType
   *         required: true
   *         schema:
   *           type: string
   *           enum: [web, whatsapp, telegram, slack]
   *     responses:
   *       200:
   *         description: Channel configuration retrieved
   */
  getChannelConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId, channelType } = req.params;
      const config = await this.botService.getChannelConfig(botId, channelType);

      res.json(successResponse(config));
    } catch (error) {
      logger.error("Error getting channel config:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "GET_CHANNEL_CONFIG_ERROR",
          message: "Failed to get channel configuration",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{botId}/clone:
   *   post:
   *     summary: Clone a bot
   *     description: Creates a new bot as a clone of the specified bot
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot cloned successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
               properties:
                 success:
                   type: boolean
                 data:
                   type: object
                 timestamp:
                   type: string
                   format: date-time
   */

  cloneBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId } = req.params;
      const userId = (req as any).user?.id;
      const bot = await this.botService.cloneBot(botId, userId);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in cloneBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to clone bot" }));
    }
  };

  createChannelIntegration = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId } = req.params;
      const channelData = req.body;
      const integration = await this.botService.createChannelIntegration(botId, channelData);

      res.status(201).json(successResponse(integration));
    } catch (error) {
      logger.error("Error creating channel integration:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "CREATE_CHANNEL_ERROR",
          message: "Failed to create channel integration",
        }),
      );
    }
  };

  updateChannelIntegration = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId, channelId } = req.params;
      const updateData = req.body;
      const integration = await this.botService.updateChannelIntegration(
        botId,
        channelId,
        updateData,
      );

      res.json(successResponse(integration));
    } catch (error) {
      logger.error("Error updating channel integration:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "UPDATE_CHANNEL_ERROR",
          message: "Failed to update channel integration",
        }),
      );
    }
  };
}
