{"passed": 8, "failed": 0, "tests": [{"name": "Health Endpoint", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:24:10.878Z"}, {"name": "GET /api/v1/bots (List)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:24:10.890Z"}, {"name": "GET /api/v1/bots/{id} (Single)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:24:10.895Z"}, {"name": "Bot 404 Error <PERSON>ling", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:24:10.896Z"}, {"name": "GET /api/v1/entities (List)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:24:10.898Z"}, {"name": "Swagger JSON Endpoint", "status": "PASS", "details": "27 endpoints documented", "timestamp": "2025-07-19T17:24:10.901Z"}, {"name": "Swagger UI Endpoint", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:24:10.905Z"}, {"name": "ApiResponse Schema Compliance", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:24:10.906Z"}]}