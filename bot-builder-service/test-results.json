{"passed": 8, "failed": 0, "tests": [{"name": "Health Endpoint", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:19:28.713Z"}, {"name": "GET /api/v1/bots (List)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:19:28.729Z"}, {"name": "GET /api/v1/bots/{id} (Single)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:19:28.738Z"}, {"name": "Bot 404 Error <PERSON>ling", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:19:28.740Z"}, {"name": "GET /api/v1/entities (List)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:19:28.742Z"}, {"name": "Swagger JSON Endpoint", "status": "PASS", "details": "27 endpoints documented", "timestamp": "2025-07-19T17:19:28.748Z"}, {"name": "Swagger UI Endpoint", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:19:28.755Z"}, {"name": "ApiResponse Schema Compliance", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:19:28.758Z"}]}